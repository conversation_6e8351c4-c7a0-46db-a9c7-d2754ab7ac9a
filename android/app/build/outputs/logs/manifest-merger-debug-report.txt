-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:1:1-31:12
MERGED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:1:1-31:12
INJECTED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:react-native-gesture-handler] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-19:12
MERGED from [:react-native-edge-to-edge] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-edge-to-edge/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-18:12
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/c41c0a1db40479f33d891af06550e7a7/transformed/expo.modules.font-13.3.2/AndroidManifest.xml:2:1-7:12
MERGED from [BareExpo:expo.modules.image:2.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e533a0c7261c43bc64b3c129c6e0e23/transformed/expo.modules.image-2.3.2/AndroidManifest.xml:2:1-19:12
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.10] /Users/<USER>/.gradle/caches/8.13/transforms/67d48c5e254af6c4ce9ec61f25cf5235/transformed/expo.modules.splashscreen-0.30.10/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.10] /Users/<USER>/.gradle/caches/8.13/transforms/64bdd59c9de07fc9b89051e8f03806a8/transformed/expo.modules.systemui-5.0.10/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:2:1-24:12
MERGED from [:expo-constants] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/bdeef16b1312dc3dd41940111e1a6fb3/transformed/expo.modules.asset-11.1.7/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/1439498032ce0c8b52a3fc0d2a0054ca/transformed/expo.modules.blur-14.1.5/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:2:1-33:12
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/d943a3268179f90aa0f1ccacbae62665/transformed/expo.modules.haptics-14.1.4/AndroidManifest.xml:2:1-9:12
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/a28908b56ec11751b59243b1f242702a/transformed/expo.modules.keepawake-14.1.4/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.linking:7.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/478a7daaf5df250b58ae7be0b2bd8ba4/transformed/expo.modules.linking-7.1.7/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] /Users/<USER>/.gradle/caches/8.13/transforms/aa71ddfffa468141c4e43e0245303cf6/transformed/core-splashscreen-1.2.0-alpha02/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/067eb4768d0977cc253e099ab589c211/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/7ceefd5bc7d6e30a327f1509056f856d/transformed/glide-plugin-3.0.5/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:2:1-11:12
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/c02f7be09f8461e07996d25cc4d306d9/transformed/awebp-3.0.5/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/51930be23003f579f3d420f5dae068a5/transformed/apng-3.0.5/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/dd9562f437d7a954611986333f652e5f/transformed/gif-3.0.5/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/5866d1f795aea78dbd8ccf03b464fd9b/transformed/avif-3.0.5/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/6e87983048c5d0adb4e5bf192cad8685/transformed/frameanimation-3.0.5/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/506e255793c8bac2342ad449777733c1/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5fde982e3dd990d3004e359630a81b79/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/5aa1ec42bdb188598da4a46342470b57/transformed/avif-integration-4.16.0/AndroidManifest.xml:2:1-9:12
MERGED from [jp.wasabeef:glide-transformations:4.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/72d39494f24b2cfae9568848e697a111/transformed/glide-transformations-4.3.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:2:1-16:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/bbeeba403c008876f9fc01e5e82c994b/transformed/glide-4.16.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/65ba2f9fa3c9fe83e5152e0f7195116c/transformed/viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/22d97c11c7e69f31bac4e14a1df1d05a/transformed/fragment-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce37a28011a1b60b4e3d132dbd8b1975/transformed/activity-1.8.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/0b3cd90608d731515ee5b0c8986ce7d6/transformed/activity-ktx-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/6d8784d24425b9166c30f1a4f5571e15/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2ecacc9a77cadee2dd15a52b568a98f1/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/94f09ae075da36273dd5b0f48955e034/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/c3179e0f1b67cb33410fa674edb5496c/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/9ad300542b889ba7bb927e78b002dd6f/transformed/webkit-1.4.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/50fb524026069718687c4ab652c29053/transformed/autofill-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/0fd0e4fa611fd75b8ba4eda3c0af56e5/transformed/animated-gif-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/76328da2fdf1aca21629387770b9d62c/transformed/webpsupport-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/4585a14f1ae9a66565345740533a24e2/transformed/fresco-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/11cf39d0535535ef53e14603004c5e44/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f3cd27b890a8689bdecbf95693f6abf/transformed/animated-base-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e843b317ee776dce67bc96b95fa8519e/transformed/animated-drawable-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/43cd96b77f258442464a756005d7068b/transformed/vito-options-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/90554224eff49d6253bc7f46090a200f/transformed/drawee-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/27d6ba0419c23ccfd28f0d65dba268b6/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/33b3f812d0a9954143308667fe9e3f2b/transformed/memory-type-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/a7b8bc68d236b8b6e7876128119c56df/transformed/memory-type-java-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/433663fad8d0a369f9eebadfaf8d9c89/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3c5f6b0c155786f68981012d876a0286/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22dca7957a69d0d46a2b301d1f3405bd/transformed/imagepipeline-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/d87c01d0ffee9e478b5ed3aedb0a67be/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7e8ff34aa003f1e3d34ab42e7a02199e/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/42f63906eff2f4cf34b7774f8da390db/transformed/urimod-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/c02947a63af0bbe4d1d82d147698eaf8/transformed/vito-source-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cfd2bc4667cb4cb0287aae0978df24ac/transformed/middleware-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3356ac95f0115ef5184f7de4471e60d4/transformed/ui-common-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ad8c48db4554b56c12f5e68dfae98b63/transformed/soloader-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/971b53c391360c5dc5f7c648986b09b6/transformed/fbcore-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/4bae3fed21d5f02d6bf43c03532e29f6/transformed/core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cd10ac37c6d4674d33088c916aa639d9/transformed/browser-1.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/925e579c481c06349ca2c1bdadbdec35/transformed/transition-1.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/34bb67911f8414f1ff69475eb4175853/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/74502918765ad8f81a97aa9e7ecb2fe7/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/8b13948c497b3cb25b633e3d026cff9f/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/69082f68bb02655e2ffff4dadfa40175/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ec1853d0b0976e32a4f952e316c0c604/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/6183b82bc0c6a1834671e2c74c065f3b/transformed/media-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/fcba58dc1b88ff592a0c160f99cb9936/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/34564e277d995fd6c7ae4ce24f36e074/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce7fe74a7e1d15baec12eb1258cf58fd/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/2db4240268c509e9b190ca1a393ca7d5/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7776c4e06fd334733f4fe348ed5c13e9/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/625e222979d00247a55c8d8eb69453bf/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b4c4dee6b60fa7565c562d06ae78c19e/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/98eec38dab2893f9dde1642e7a91ed70/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/44911df0b7c9625b5575bd1446144add/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/2af3bcc447ac110862396e7d0b0ec9ae/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e45932910c15ea66169daadac963681/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/8fbfdc943d04e82c10add9c26833a5c6/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fa08d204398219a1a89b242b31fa3041/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/5379c5771c0cc88a961c78af9bb09339/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/c4b770dc8989251f37e56402d8aa1685/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/19f8795450958262f34e41d8151462d1/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/5e0f9e6bf46b1bd6aeea3c4a7ba42299/transformed/tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/05c532ea6eeed5ba96fff64378302997/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e69ea34ae74c1f0868245de2a10f55c0/transformed/ui-core-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/2d443ea4419c363d5cc1fea0dc777e76/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/9f527975834136bd1e4e42d80a2ffd4f/transformed/vito-renderer-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/35675df85bd02bb3d2682c8049acc4ba/transformed/hermes-android-0.79.5-debug/AndroidManifest.xml:2:1-7:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.13/transforms/87e4c57220eac6c2a196044df99f723d/transformed/BlurView-version-2.0.6/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/16ea918983671f6172c82665df93c2bc/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5745f858b74ff0a2f8e17d4b26e19812/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0e6b28d36dd1dcea484117e191bbf338/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/67803a5cacc8f200daadaf81b44424d4/transformed/gifdecoder-4.16.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.13/transforms/5e715ff8949f07ccecbd27a8f3ccfe53/transformed/exifinterface-1.3.6/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/63bd8e0afbc574e96db736c0dfd2bf6a/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/839cc9c259c516d7f6f4a5b066ac62ea/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/497868229bdb3c0c11614fe7fa96dcc3/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3b4cadc5d581274a383a17f34b96bacf/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:2:1-17:12
MERGED from [com.caverock:androidsvg-aar:1.4] /Users/<USER>/.gradle/caches/8.13/transforms/ac9879374d2e96acc4c0b73efbd01c6c/transformed/androidsvg-aar-1.4/AndroidManifest.xml:2:1-11:12
MERGED from [org.aomedia.avif.android:avif:1.1.1.14d8e3c4] /Users/<USER>/.gradle/caches/8.13/transforms/28adfac15bc287e2589f007a216b599e/transformed/avif-1.1.1.14d8e3c4/AndroidManifest.xml:2:1-7:12
	package
		INJECTED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:2:3-64
MERGED from [BareExpo:expo.modules.image:2.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e533a0c7261c43bc64b3c129c6e0e23/transformed/expo.modules.image-2.3.2/AndroidManifest.xml:7:5-67
MERGED from [BareExpo:expo.modules.image:2.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e533a0c7261c43bc64b3c129c6e0e23/transformed/expo.modules.image-2.3.2/AndroidManifest.xml:7:5-67
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:8:5-67
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:8:5-67
	android:name
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:2:20-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:3:3-77
MERGED from [BareExpo:expo.modules.image:2.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e533a0c7261c43bc64b3c129c6e0e23/transformed/expo.modules.image-2.3.2/AndroidManifest.xml:15:5-17:38
MERGED from [BareExpo:expo.modules.image:2.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e533a0c7261c43bc64b3c129c6e0e23/transformed/expo.modules.image-2.3.2/AndroidManifest.xml:15:5-17:38
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:10:5-80
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:10:5-80
	android:maxSdkVersion
		ADDED from [BareExpo:expo.modules.image:2.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e533a0c7261c43bc64b3c129c6e0e23/transformed/expo.modules.image-2.3.2/AndroidManifest.xml:17:9-35
	android:name
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:3:20-75
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:4:3-75
MERGED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:4:3-75
MERGED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:4:3-75
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:16:5-78
	android:name
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:4:20-73
uses-permission#android.permission.VIBRATE
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:5:3-63
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/d943a3268179f90aa0f1ccacbae62665/transformed/expo.modules.haptics-14.1.4/AndroidManifest.xml:7:5-66
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/d943a3268179f90aa0f1ccacbae62665/transformed/expo.modules.haptics-14.1.4/AndroidManifest.xml:7:5-66
	android:name
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:5:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:6:3-78
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:9:5-81
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:9:5-81
	android:name
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:6:20-76
queries
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:7:3-13:13
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:7:5-13:15
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:7:5-13:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:8:5-12:14
action#android.intent.action.VIEW
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:9:7-58
	android:name
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:9:15-56
category#android.intent.category.BROWSABLE
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:10:7-67
	android:name
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:10:17-65
data
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:11:7-37
	android:scheme
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:11:13-35
application
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:3-30:17
MERGED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:3-30:17
MERGED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:3-30:17
INJECTED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml:6:5-162
MERGED from [:react-native-webview] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webview] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-17:19
MERGED from [:expo-modules-core] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:18:5-22:19
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:20:5-31:19
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:20:5-31:19
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:10:5-14:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:10:5-14:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.13/transforms/87e4c57220eac6c2a196044df99f723d/transformed/BlurView-version-2.0.6/AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.13/transforms/87e4c57220eac6c2a196044df99f723d/transformed/BlurView-version-2.0.6/AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
MERGED from [org.aomedia.avif.android:avif:1.1.1.14d8e3c4] /Users/<USER>/.gradle/caches/8.13/transforms/28adfac15bc287e2589f007a216b599e/transformed/avif-1.1.1.14d8e3c4/AndroidManifest.xml:5:5-6:19
MERGED from [org.aomedia.avif.android:avif:1.1.1.14d8e3c4] /Users/<USER>/.gradle/caches/8.13/transforms/28adfac15bc287e2589f007a216b599e/transformed/avif-1.1.1.14d8e3c4/AndroidManifest.xml:5:5-6:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:221-247
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:221-247
	android:label
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:48-80
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:48-80
	tools:ignore
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:116-161
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:116-161
	tools:targetApi
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml:6:54-74
	android:icon
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:81-115
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:81-115
	android:allowBackup
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:162-188
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:162-188
	android:theme
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:189-220
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:189-220
	tools:replace
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml:6:18-53
	android:name
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:16-47
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:14:16-47
meta-data#expo.modules.updates.ENABLED
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:15:5-83
	android:value
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:15:60-81
	android:name
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:15:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:16:5-105
	android:value
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:16:81-103
	android:name
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:16:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:17:5-99
	android:value
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:17:80-97
	android:name
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:17:16-79
activity#com.harsharora.safqashipping.MainActivity
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:18:5-29:16
	android:screenOrientation
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:18:280-316
	android:launchMode
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:18:135-166
	android:windowSoftInputMode
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:18:167-209
	android:exported
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:18:256-279
	android:configChanges
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:18:44-134
	android:theme
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:18:210-255
	android:name
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:18:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:19:7-22:23
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:20:9-60
	android:name
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:20:17-58
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:21:9-68
	android:name
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:21:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:safqashipping
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:23:7-28:23
category#android.intent.category.DEFAULT
ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:25:9-67
	android:name
		ADDED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/main/AndroidManifest.xml:25:19-65
uses-sdk
INJECTED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml
MERGED from [:react-native-gesture-handler] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-edge-to-edge/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-edge-to-edge/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/c41c0a1db40479f33d891af06550e7a7/transformed/expo.modules.font-13.3.2/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/c41c0a1db40479f33d891af06550e7a7/transformed/expo.modules.font-13.3.2/AndroidManifest.xml:5:5-44
MERGED from [BareExpo:expo.modules.image:2.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e533a0c7261c43bc64b3c129c6e0e23/transformed/expo.modules.image-2.3.2/AndroidManifest.xml:4:5-44
MERGED from [BareExpo:expo.modules.image:2.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e533a0c7261c43bc64b3c129c6e0e23/transformed/expo.modules.image-2.3.2/AndroidManifest.xml:4:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.10] /Users/<USER>/.gradle/caches/8.13/transforms/67d48c5e254af6c4ce9ec61f25cf5235/transformed/expo.modules.splashscreen-0.30.10/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.10] /Users/<USER>/.gradle/caches/8.13/transforms/67d48c5e254af6c4ce9ec61f25cf5235/transformed/expo.modules.splashscreen-0.30.10/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.10] /Users/<USER>/.gradle/caches/8.13/transforms/64bdd59c9de07fc9b89051e8f03806a8/transformed/expo.modules.systemui-5.0.10/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.10] /Users/<USER>/.gradle/caches/8.13/transforms/64bdd59c9de07fc9b89051e8f03806a8/transformed/expo.modules.systemui-5.0.10/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:10:5-44
MERGED from [:expo-constants] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/bdeef16b1312dc3dd41940111e1a6fb3/transformed/expo.modules.asset-11.1.7/AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/bdeef16b1312dc3dd41940111e1a6fb3/transformed/expo.modules.asset-11.1.7/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/1439498032ce0c8b52a3fc0d2a0054ca/transformed/expo.modules.blur-14.1.5/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/1439498032ce0c8b52a3fc0d2a0054ca/transformed/expo.modules.blur-14.1.5/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/d943a3268179f90aa0f1ccacbae62665/transformed/expo.modules.haptics-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/d943a3268179f90aa0f1ccacbae62665/transformed/expo.modules.haptics-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/a28908b56ec11751b59243b1f242702a/transformed/expo.modules.keepawake-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/a28908b56ec11751b59243b1f242702a/transformed/expo.modules.keepawake-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.linking:7.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/478a7daaf5df250b58ae7be0b2bd8ba4/transformed/expo.modules.linking-7.1.7/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.linking:7.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/478a7daaf5df250b58ae7be0b2bd8ba4/transformed/expo.modules.linking-7.1.7/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] /Users/<USER>/.gradle/caches/8.13/transforms/aa71ddfffa468141c4e43e0245303cf6/transformed/core-splashscreen-1.2.0-alpha02/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] /Users/<USER>/.gradle/caches/8.13/transforms/aa71ddfffa468141c4e43e0245303cf6/transformed/core-splashscreen-1.2.0-alpha02/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/067eb4768d0977cc253e099ab589c211/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/067eb4768d0977cc253e099ab589c211/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/7ceefd5bc7d6e30a327f1509056f856d/transformed/glide-plugin-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/7ceefd5bc7d6e30a327f1509056f856d/transformed/glide-plugin-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/c02f7be09f8461e07996d25cc4d306d9/transformed/awebp-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/c02f7be09f8461e07996d25cc4d306d9/transformed/awebp-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/51930be23003f579f3d420f5dae068a5/transformed/apng-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/51930be23003f579f3d420f5dae068a5/transformed/apng-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/dd9562f437d7a954611986333f652e5f/transformed/gif-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/dd9562f437d7a954611986333f652e5f/transformed/gif-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/5866d1f795aea78dbd8ccf03b464fd9b/transformed/avif-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/5866d1f795aea78dbd8ccf03b464fd9b/transformed/avif-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/6e87983048c5d0adb4e5bf192cad8685/transformed/frameanimation-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.5] /Users/<USER>/.gradle/caches/8.13/transforms/6e87983048c5d0adb4e5bf192cad8685/transformed/frameanimation-3.0.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/506e255793c8bac2342ad449777733c1/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/506e255793c8bac2342ad449777733c1/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5fde982e3dd990d3004e359630a81b79/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5fde982e3dd990d3004e359630a81b79/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/5aa1ec42bdb188598da4a46342470b57/transformed/avif-integration-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/5aa1ec42bdb188598da4a46342470b57/transformed/avif-integration-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/72d39494f24b2cfae9568848e697a111/transformed/glide-transformations-4.3.0/AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/72d39494f24b2cfae9568848e697a111/transformed/glide-transformations-4.3.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/bbeeba403c008876f9fc01e5e82c994b/transformed/glide-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/bbeeba403c008876f9fc01e5e82c994b/transformed/glide-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/65ba2f9fa3c9fe83e5152e0f7195116c/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/65ba2f9fa3c9fe83e5152e0f7195116c/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/22d97c11c7e69f31bac4e14a1df1d05a/transformed/fragment-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/22d97c11c7e69f31bac4e14a1df1d05a/transformed/fragment-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce37a28011a1b60b4e3d132dbd8b1975/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce37a28011a1b60b4e3d132dbd8b1975/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/0b3cd90608d731515ee5b0c8986ce7d6/transformed/activity-ktx-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/0b3cd90608d731515ee5b0c8986ce7d6/transformed/activity-ktx-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/6d8784d24425b9166c30f1a4f5571e15/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/6d8784d24425b9166c30f1a4f5571e15/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2ecacc9a77cadee2dd15a52b568a98f1/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2ecacc9a77cadee2dd15a52b568a98f1/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/94f09ae075da36273dd5b0f48955e034/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/94f09ae075da36273dd5b0f48955e034/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/c3179e0f1b67cb33410fa674edb5496c/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/c3179e0f1b67cb33410fa674edb5496c/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/9ad300542b889ba7bb927e78b002dd6f/transformed/webkit-1.4.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/9ad300542b889ba7bb927e78b002dd6f/transformed/webkit-1.4.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/50fb524026069718687c4ab652c29053/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/50fb524026069718687c4ab652c29053/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/0fd0e4fa611fd75b8ba4eda3c0af56e5/transformed/animated-gif-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/0fd0e4fa611fd75b8ba4eda3c0af56e5/transformed/animated-gif-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/76328da2fdf1aca21629387770b9d62c/transformed/webpsupport-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/76328da2fdf1aca21629387770b9d62c/transformed/webpsupport-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/4585a14f1ae9a66565345740533a24e2/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/4585a14f1ae9a66565345740533a24e2/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/11cf39d0535535ef53e14603004c5e44/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/11cf39d0535535ef53e14603004c5e44/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f3cd27b890a8689bdecbf95693f6abf/transformed/animated-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f3cd27b890a8689bdecbf95693f6abf/transformed/animated-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e843b317ee776dce67bc96b95fa8519e/transformed/animated-drawable-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e843b317ee776dce67bc96b95fa8519e/transformed/animated-drawable-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/43cd96b77f258442464a756005d7068b/transformed/vito-options-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/43cd96b77f258442464a756005d7068b/transformed/vito-options-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/90554224eff49d6253bc7f46090a200f/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/90554224eff49d6253bc7f46090a200f/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/27d6ba0419c23ccfd28f0d65dba268b6/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/27d6ba0419c23ccfd28f0d65dba268b6/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/33b3f812d0a9954143308667fe9e3f2b/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/33b3f812d0a9954143308667fe9e3f2b/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/a7b8bc68d236b8b6e7876128119c56df/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/a7b8bc68d236b8b6e7876128119c56df/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/433663fad8d0a369f9eebadfaf8d9c89/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/433663fad8d0a369f9eebadfaf8d9c89/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3c5f6b0c155786f68981012d876a0286/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3c5f6b0c155786f68981012d876a0286/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22dca7957a69d0d46a2b301d1f3405bd/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22dca7957a69d0d46a2b301d1f3405bd/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/d87c01d0ffee9e478b5ed3aedb0a67be/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/d87c01d0ffee9e478b5ed3aedb0a67be/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7e8ff34aa003f1e3d34ab42e7a02199e/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7e8ff34aa003f1e3d34ab42e7a02199e/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/42f63906eff2f4cf34b7774f8da390db/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/42f63906eff2f4cf34b7774f8da390db/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/c02947a63af0bbe4d1d82d147698eaf8/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/c02947a63af0bbe4d1d82d147698eaf8/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cfd2bc4667cb4cb0287aae0978df24ac/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cfd2bc4667cb4cb0287aae0978df24ac/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3356ac95f0115ef5184f7de4471e60d4/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3356ac95f0115ef5184f7de4471e60d4/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ad8c48db4554b56c12f5e68dfae98b63/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ad8c48db4554b56c12f5e68dfae98b63/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/971b53c391360c5dc5f7c648986b09b6/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/971b53c391360c5dc5f7c648986b09b6/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/4bae3fed21d5f02d6bf43c03532e29f6/transformed/core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/4bae3fed21d5f02d6bf43c03532e29f6/transformed/core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cd10ac37c6d4674d33088c916aa639d9/transformed/browser-1.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cd10ac37c6d4674d33088c916aa639d9/transformed/browser-1.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/925e579c481c06349ca2c1bdadbdec35/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/925e579c481c06349ca2c1bdadbdec35/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/34bb67911f8414f1ff69475eb4175853/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/34bb67911f8414f1ff69475eb4175853/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/74502918765ad8f81a97aa9e7ecb2fe7/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/74502918765ad8f81a97aa9e7ecb2fe7/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/8b13948c497b3cb25b633e3d026cff9f/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/8b13948c497b3cb25b633e3d026cff9f/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/69082f68bb02655e2ffff4dadfa40175/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/69082f68bb02655e2ffff4dadfa40175/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ec1853d0b0976e32a4f952e316c0c604/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ec1853d0b0976e32a4f952e316c0c604/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/6183b82bc0c6a1834671e2c74c065f3b/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/6183b82bc0c6a1834671e2c74c065f3b/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/fcba58dc1b88ff592a0c160f99cb9936/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/fcba58dc1b88ff592a0c160f99cb9936/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/34564e277d995fd6c7ae4ce24f36e074/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/34564e277d995fd6c7ae4ce24f36e074/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce7fe74a7e1d15baec12eb1258cf58fd/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce7fe74a7e1d15baec12eb1258cf58fd/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/2db4240268c509e9b190ca1a393ca7d5/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/2db4240268c509e9b190ca1a393ca7d5/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7776c4e06fd334733f4fe348ed5c13e9/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7776c4e06fd334733f4fe348ed5c13e9/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/625e222979d00247a55c8d8eb69453bf/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/625e222979d00247a55c8d8eb69453bf/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b4c4dee6b60fa7565c562d06ae78c19e/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b4c4dee6b60fa7565c562d06ae78c19e/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/98eec38dab2893f9dde1642e7a91ed70/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/98eec38dab2893f9dde1642e7a91ed70/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/44911df0b7c9625b5575bd1446144add/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/44911df0b7c9625b5575bd1446144add/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/2af3bcc447ac110862396e7d0b0ec9ae/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/2af3bcc447ac110862396e7d0b0ec9ae/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e45932910c15ea66169daadac963681/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e45932910c15ea66169daadac963681/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/8fbfdc943d04e82c10add9c26833a5c6/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/8fbfdc943d04e82c10add9c26833a5c6/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fa08d204398219a1a89b242b31fa3041/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fa08d204398219a1a89b242b31fa3041/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/5379c5771c0cc88a961c78af9bb09339/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/5379c5771c0cc88a961c78af9bb09339/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/c4b770dc8989251f37e56402d8aa1685/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/c4b770dc8989251f37e56402d8aa1685/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/19f8795450958262f34e41d8151462d1/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/19f8795450958262f34e41d8151462d1/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/5e0f9e6bf46b1bd6aeea3c4a7ba42299/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/5e0f9e6bf46b1bd6aeea3c4a7ba42299/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/05c532ea6eeed5ba96fff64378302997/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/05c532ea6eeed5ba96fff64378302997/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e69ea34ae74c1f0868245de2a10f55c0/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e69ea34ae74c1f0868245de2a10f55c0/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/2d443ea4419c363d5cc1fea0dc777e76/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/2d443ea4419c363d5cc1fea0dc777e76/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/9f527975834136bd1e4e42d80a2ffd4f/transformed/vito-renderer-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/9f527975834136bd1e4e42d80a2ffd4f/transformed/vito-renderer-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/35675df85bd02bb3d2682c8049acc4ba/transformed/hermes-android-0.79.5-debug/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/35675df85bd02bb3d2682c8049acc4ba/transformed/hermes-android-0.79.5-debug/AndroidManifest.xml:5:5-44
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.13/transforms/87e4c57220eac6c2a196044df99f723d/transformed/BlurView-version-2.0.6/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] /Users/<USER>/.gradle/caches/8.13/transforms/87e4c57220eac6c2a196044df99f723d/transformed/BlurView-version-2.0.6/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/16ea918983671f6172c82665df93c2bc/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/16ea918983671f6172c82665df93c2bc/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5745f858b74ff0a2f8e17d4b26e19812/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5745f858b74ff0a2f8e17d4b26e19812/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0e6b28d36dd1dcea484117e191bbf338/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0e6b28d36dd1dcea484117e191bbf338/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/67803a5cacc8f200daadaf81b44424d4/transformed/gifdecoder-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] /Users/<USER>/.gradle/caches/8.13/transforms/67803a5cacc8f200daadaf81b44424d4/transformed/gifdecoder-4.16.0/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.13/transforms/5e715ff8949f07ccecbd27a8f3ccfe53/transformed/exifinterface-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.13/transforms/5e715ff8949f07ccecbd27a8f3ccfe53/transformed/exifinterface-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/63bd8e0afbc574e96db736c0dfd2bf6a/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/63bd8e0afbc574e96db736c0dfd2bf6a/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/839cc9c259c516d7f6f4a5b066ac62ea/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/839cc9c259c516d7f6f4a5b066ac62ea/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/497868229bdb3c0c11614fe7fa96dcc3/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/497868229bdb3c0c11614fe7fa96dcc3/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3b4cadc5d581274a383a17f34b96bacf/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3b4cadc5d581274a383a17f34b96bacf/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] /Users/<USER>/.gradle/caches/8.13/transforms/ac9879374d2e96acc4c0b73efbd01c6c/transformed/androidsvg-aar-1.4/AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] /Users/<USER>/.gradle/caches/8.13/transforms/ac9879374d2e96acc4c0b73efbd01c6c/transformed/androidsvg-aar-1.4/AndroidManifest.xml:7:5-9:41
MERGED from [org.aomedia.avif.android:avif:1.1.1.14d8e3c4] /Users/<USER>/.gradle/caches/8.13/transforms/28adfac15bc287e2589f007a216b599e/transformed/avif-1.1.1.14d8e3c4/AndroidManifest.xml:4:5-44
MERGED from [org.aomedia.avif.android:avif:1.1.1.14d8e3c4] /Users/<USER>/.gradle/caches/8.13/transforms/28adfac15bc287e2589f007a216b599e/transformed/avif-1.1.1.14d8e3c4/AndroidManifest.xml:4:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Desktop/react-native/SafqaShipping/android/app/src/debug/AndroidManifest.xml
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-webview] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-64
	android:exported
		ADDED from [:react-native-webview] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-webview] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-15:63
	android:resource
		ADDED from [:react-native-webview] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-60
	android:name
		ADDED from [:react-native-webview] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:17-67
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] /Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-57
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [BareExpo:expo.modules.image:2.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e533a0c7261c43bc64b3c129c6e0e23/transformed/expo.modules.image-2.3.2/AndroidManifest.xml:12:5-79
	android:name
		ADDED from [BareExpo:expo.modules.image:2.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e533a0c7261c43bc64b3c129c6e0e23/transformed/expo.modules.image-2.3.2/AndroidManifest.xml:12:22-76
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:20:13-77
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:13-79
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:26:13-48
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:22:13-74
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:8:9-12:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:11:13-90
	android:name
		ADDED from [host.exp.exponent:expo.modules.webbrowser:14.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/a251dd56850b31b3204c455b354971bf/transformed/expo.modules.webbrowser-14.2.0/AndroidManifest.xml:11:21-87
meta-data#com.bumptech.glide.integration.okhttp3.OkHttpGlideModule
ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:11:9-13:43
	android:value
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:13:13-40
	android:name
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] /Users/<USER>/.gradle/caches/8.13/transforms/ea759b4c200c5e1eb197cb4831c5d9c1/transformed/okhttp3-integration-4.11.0/AndroidManifest.xml:12:13-84
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.harsharora.safqashipping.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.harsharora.safqashipping.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
