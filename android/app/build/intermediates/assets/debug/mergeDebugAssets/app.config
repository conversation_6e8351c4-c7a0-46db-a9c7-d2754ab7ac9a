{"name": "Safqa Shipping", "slug": "safqa-shipping", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "safqashipping", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#fff"}, "ios": {"supportsTablet": true, "icon": "./assets/images/icon.png", "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#fff"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#fff"}, "edgeToEdgeEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#fff"}, "package": "com.harsharora.safqashipping"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png", "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#fff"}}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#fff"}]], "experiments": {"typedRoutes": true}, "sdkVersion": "53.0.0", "platforms": ["ios", "android", "web"], "extra": {"router": {}}, "androidStatusBar": {"backgroundColor": "#fff"}}