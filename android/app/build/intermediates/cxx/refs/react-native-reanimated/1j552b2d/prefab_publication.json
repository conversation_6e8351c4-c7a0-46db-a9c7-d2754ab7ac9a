{"installationFolder": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-reanimated/android/build/intermediates/prefab_package/debug/prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageVersion": "3.17.5", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-reanimated/android/build/prefab-headers/reanimated", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/6u6q2r64/obj/arm64-v8a/libreanimated.so", "abiAndroidGradleBuildJsonFile": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-reanimated/android/.cxx/Debug/6u6q2r64/arm64-v8a/android_gradle_build.json"}]}, {"moduleName": "worklets", "moduleHeaders": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-reanimated/android/build/prefab-headers/worklets", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/6u6q2r64/obj/arm64-v8a/libworklets.so", "abiAndroidGradleBuildJsonFile": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-reanimated/android/.cxx/Debug/6u6q2r64/arm64-v8a/android_gradle_build.json"}]}]}}