{"logs": [{"outputFile": "com.harsharora.safqashipping.app-mergeDebugResources-56:/values-v23/values-v23.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/res/values-v23/values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,39,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1225,1975,2094,2221,2443,2667,2782,2889,3002", "endLines": "2,3,4,5,19,33,34,35,38,42,43,44,45,49", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "185,320,395,482,1220,1970,2089,2216,2438,2662,2777,2884,2997,3227"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/5745f858b74ff0a2f8e17d4b26e19812/transformed/cardview-1.0.0/res/values-v23/values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "3232", "endLines": "52", "endColumns": "12", "endOffsets": "3377"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/res/values-v23/values-v23.xml", "from": {"startLines": "2,5,9,13,16,19,22,25,28,32", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,271,413,574,784,991,1198,1401,1603,1868", "endLines": "4,8,12,15,18,21,24,27,31,35", "endColumns": "10,10,10,10,10,10,10,10,10,10", "endOffsets": "266,408,569,779,986,1193,1396,1598,1863,2136"}, "to": {"startLines": "53,56,60,64,67,70,73,76,79,83", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3382,3598,3740,3901,4111,4318,4525,4728,4930,5195", "endLines": "55,59,63,66,69,72,75,78,82,86", "endColumns": "10,10,10,10,10,10,10,10,10,10", "endOffsets": "3593,3735,3896,4106,4313,4520,4723,4925,5190,5463"}}]}]}