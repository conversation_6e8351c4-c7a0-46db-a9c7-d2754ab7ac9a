{"logs": [{"outputFile": "com.harsharora.safqashipping.app-mergeDebugResources-56:/values-kn/values-kn.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/res/values-kn/values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,529,636,762,840,916,1007,1100,1195,1289,1389,1482,1577,1671,1762,1853,1935,2051,2161,2260,2373,2478,2592,2756,2856", "endColumns": "113,111,112,84,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,524,631,757,835,911,1002,1095,1190,1284,1384,1477,1572,1666,1757,1848,1930,2046,2156,2255,2368,2473,2587,2751,2851,2934"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "327,441,553,666,751,858,984,1062,1138,1229,1322,1417,1511,1611,1704,1799,1893,1984,2075,2157,2273,2383,2482,2595,2700,2814,2978,11123", "endColumns": "113,111,112,84,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "436,548,661,746,853,979,1057,1133,1224,1317,1412,1506,1606,1699,1794,1888,1979,2070,2152,2268,2378,2477,2590,2695,2809,2973,3073,11201"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/res/values-kn/values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "39,40,41,42,43,44,45,141", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3609,3707,3810,3911,4017,4118,4226,12091", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "3702,3805,3906,4012,4113,4221,4349,12187"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/res/values-kn/values-kn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,277,361,444,526,641,736,843,956,1041,1098,1161,1255,1321,1383,1486,1552,1623,1682,1758,1823,1877,1990,2048,2109,2163,2242,2358,2444,2527,2622,2708,2799,2941,3020,3099,3228,3316,3400,3457,3509,3575,3655,3745,3816,3895,3972,4049,4126,4195,4312,4411,4488,4581,4676,4750,4831,4927,4978,5062,5130,5216,5304,5367,5432,5495,5563,5668,5773,5868,5971,6032,6088,6170,6262,6341", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,82,81,114,94,106,112,84,56,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,85,82,94,85,90,141,78,78,128,87,83,56,51,65,79,89,70,78,76,76,76,68,116,98,76,92,94,73,80,95,50,83,67,85,87,62,64,62,67,104,104,94,102,60,55,81,91,78,73", "endOffsets": "272,356,439,521,636,731,838,951,1036,1093,1156,1250,1316,1378,1481,1547,1618,1677,1753,1818,1872,1985,2043,2104,2158,2237,2353,2439,2522,2617,2703,2794,2936,3015,3094,3223,3311,3395,3452,3504,3570,3650,3740,3811,3890,3967,4044,4121,4190,4307,4406,4483,4576,4671,4745,4826,4922,4973,5057,5125,5211,5299,5362,5427,5490,5558,5663,5768,5863,5966,6027,6083,6165,6257,6336,6410"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,51,52,53,58,61,63,64,65,66,67,68,69,70,71,72,73,74,75,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3150,3234,3317,3399,3514,4354,4461,4574,4852,4909,4972,5455,5671,5801,5904,5970,6041,6100,6176,6241,6295,6408,6466,6527,6581,6660,6993,7079,7162,7257,7343,7434,7576,7655,7734,7863,7951,8035,8092,8144,8210,8290,8380,8451,8530,8607,8684,8761,8830,8947,9046,9123,9216,9311,9385,9466,9562,9613,9697,9765,9851,9939,10002,10067,10130,10198,10303,10408,10503,10606,10667,10723,11206,11298,11377", "endLines": "5,34,35,36,37,38,46,47,48,51,52,53,58,61,63,64,65,66,67,68,69,70,71,72,73,74,75,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,130,131,132", "endColumns": "12,83,82,81,114,94,106,112,84,56,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,85,82,94,85,90,141,78,78,128,87,83,56,51,65,79,89,70,78,76,76,76,68,116,98,76,92,94,73,80,95,50,83,67,85,87,62,64,62,67,104,104,94,102,60,55,81,91,78,73", "endOffsets": "322,3229,3312,3394,3509,3604,4456,4569,4654,4904,4967,5061,5516,5728,5899,5965,6036,6095,6171,6236,6290,6403,6461,6522,6576,6655,6771,7074,7157,7252,7338,7429,7571,7650,7729,7858,7946,8030,8087,8139,8205,8285,8375,8446,8525,8602,8679,8756,8825,8942,9041,9118,9211,9306,9380,9461,9557,9608,9692,9760,9846,9934,9997,10062,10125,10193,10298,10403,10498,10601,10662,10718,10800,11293,11372,11446"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/res/values-kn/values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,206,278,347,428,496,563,638,713,798,879,950,1031,1111,1189,1271,1358,1435,1506,1576,1671,1743,1821,1890", "endColumns": "71,78,71,68,80,67,66,74,74,84,80,70,80,79,77,81,86,76,70,69,94,71,77,68,74", "endOffsets": "122,201,273,342,423,491,558,633,708,793,874,945,1026,1106,1184,1266,1353,1430,1501,1571,1666,1738,1816,1885,1960"}, "to": {"startLines": "33,49,57,59,60,62,76,77,78,125,126,127,128,133,134,135,136,137,138,139,140,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3078,4659,5383,5521,5590,5733,6776,6843,6918,10805,10890,10971,11042,11451,11531,11609,11691,11778,11855,11926,11996,12192,12264,12342,12411", "endColumns": "71,78,71,68,80,67,66,74,74,84,80,70,80,79,77,81,86,76,70,69,94,71,77,68,74", "endOffsets": "3145,4733,5450,5585,5666,5796,6838,6913,6988,10885,10966,11037,11118,11526,11604,11686,11773,11850,11921,11991,12086,12259,12337,12406,12481"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/cd10ac37c6d4674d33088c916aa639d9/transformed/browser-1.6.0/res/values-kn/values-kn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,269,385", "endColumns": "113,99,115,100", "endOffsets": "164,264,380,481"}, "to": {"startLines": "50,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4738,5066,5166,5282", "endColumns": "113,99,115,100", "endOffsets": "4847,5161,5277,5378"}}]}]}