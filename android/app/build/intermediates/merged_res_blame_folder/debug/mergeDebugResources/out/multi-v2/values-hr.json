{"logs": [{"outputFile": "com.harsharora.safqashipping.app-mergeDebugResources-56:/values-hr/values-hr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/res/values-hr/values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1128,1196,1292,1360,1423,1531,1591,1657,1713,1784,1844,1898,2024,2081,2143,2197,2272,2406,2491,2569,2664,2749,2830,2967,3051,3137,3270,3361,3439,3495,3550,3616,3690,3768,3839,3921,3993,4070,4150,4224,4331,4424,4497,4589,4685,4759,4835,4931,4983,5065,5132,5219,5306,5368,5432,5495,5565,5671,5787,5884,5998,6058,6117,6197,6280,6357", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "315,393,471,556,653,746,842,972,1056,1123,1191,1287,1355,1418,1526,1586,1652,1708,1779,1839,1893,2019,2076,2138,2192,2267,2401,2486,2564,2659,2744,2825,2962,3046,3132,3265,3356,3434,3490,3545,3611,3685,3763,3834,3916,3988,4065,4145,4219,4326,4419,4492,4584,4680,4754,4830,4926,4978,5060,5127,5214,5301,5363,5427,5490,5560,5666,5782,5879,5993,6053,6112,6192,6275,6352,6427"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,52,53,54,59,62,64,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3155,3233,3311,3396,3493,4312,4408,4538,4811,4878,4946,5432,5650,5780,5888,5948,6014,6070,6141,6201,6255,6381,6438,6500,6554,6629,6998,7083,7161,7256,7341,7422,7559,7643,7729,7862,7953,8031,8087,8142,8208,8282,8360,8431,8513,8585,8662,8742,8816,8923,9016,9089,9181,9277,9351,9427,9523,9575,9657,9724,9811,9898,9960,10024,10087,10157,10263,10379,10476,10590,10650,10709,11205,11288,11365", "endLines": "6,35,36,37,38,39,47,48,49,52,53,54,59,62,64,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,131,132,133", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "365,3228,3306,3391,3488,3581,4403,4533,4617,4873,4941,5037,5495,5708,5883,5943,6009,6065,6136,6196,6250,6376,6433,6495,6549,6624,6758,7078,7156,7251,7336,7417,7554,7638,7724,7857,7948,8026,8082,8137,8203,8277,8355,8426,8508,8580,8657,8737,8811,8918,9011,9084,9176,9272,9346,9422,9518,9570,9652,9719,9806,9893,9955,10019,10082,10152,10258,10374,10471,10585,10645,10704,10784,11283,11360,11435"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,213,287,356,437,504,575,656,739,823,912,984,1070,1153,1229,1309,1391,1470,1548,1624,1714,1787,1866,1944", "endColumns": "73,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "124,208,282,351,432,499,570,651,734,818,907,979,1065,1148,1224,1304,1386,1465,1543,1619,1709,1782,1861,1939,2020"}, "to": {"startLines": "34,50,58,60,61,63,77,78,79,126,127,128,129,134,135,136,137,138,139,140,141,143,144,145,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3081,4622,5358,5500,5569,5713,6763,6834,6915,10789,10873,10962,11034,11440,11523,11599,11679,11761,11840,11918,11994,12185,12258,12337,12415", "endColumns": "73,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "3150,4701,5427,5564,5645,5775,6829,6910,6993,10868,10957,11029,11115,11518,11594,11674,11756,11835,11913,11989,12079,12253,12332,12410,12491"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,475,570,677,763,867,986,1071,1153,1244,1337,1432,1526,1626,1719,1814,1909,2000,2091,2177,2281,2393,2494,2599,2713,2815,2984,11120", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "470,565,672,758,862,981,1066,1148,1239,1332,1427,1521,1621,1714,1809,1904,1995,2086,2172,2276,2388,2489,2594,2708,2810,2979,3076,11200"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/cd10ac37c6d4674d33088c916aa639d9/transformed/browser-1.6.0/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,101", "endOffsets": "155,255,369,471"}, "to": {"startLines": "51,55,56,57", "startColumns": "4,4,4,4", "startOffsets": "4706,5042,5142,5256", "endColumns": "104,99,113,101", "endOffsets": "4806,5137,5251,5353"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "40,41,42,43,44,45,46,142", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3586,3684,3791,3888,3987,4091,4195,12084", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3679,3786,3883,3982,4086,4190,4307,12180"}}]}]}