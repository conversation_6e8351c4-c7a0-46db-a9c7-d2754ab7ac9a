{"root": "/Users/<USER>/Desktop/react-native/SafqaShipping", "reactNativePath": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native", "dependencies": {"expo": {"root": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo", "name": "expo", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo/android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-gesture-handler": {"root": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-gesture-handler/android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerRootViewComponentDescriptor", "RNGestureHandlerButtonComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-reanimated": {"root": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-reanimated/android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-safe-area-context/android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-screens/android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-webview": {"root": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview", "name": "react-native-webview", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android", "packageImportPath": "import com.reactnativecommunity.webview.RNCWebViewPackage;", "packageInstance": "new RNCWebViewPackage()", "buildTypes": [], "libraryName": "RNCWebViewSpec", "componentDescriptors": ["RNCWebViewComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-edge-to-edge": {"root": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-edge-to-edge", "name": "react-native-edge-to-edge", "platforms": {"android": {"sourceDir": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-edge-to-edge/android", "packageImportPath": "import com.zoontek.rnedgetoedge.EdgeToEdgePackage;", "packageInstance": "new EdgeToEdgePackage()", "buildTypes": [], "libraryName": "RNEdgeToEdge", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.harsharora.safqashipping", "sourceDir": "/Users/<USER>/Desktop/react-native/SafqaShipping/android"}}}