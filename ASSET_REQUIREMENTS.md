# Safqa Shipping - Asset Requirements Guide

## Complete Asset Specifications

### 📱 iOS Assets

#### App Icon (icon.png)
```
Size: 1024x1024 pixels
Format: PNG (24-bit, no alpha channel)
Color Space: sRGB
Location: ./assets/images/icon.png
DPI: 72 or higher
File Size: < 1MB recommended
```

#### iOS Splash Screen
```
iPhone 14 Pro Max: 1284x2778 pixels
iPhone 14 Pro: 1179x2556 pixels  
iPhone 14: 1170x2532 pixels
iPhone SE: 750x1334 pixels
iPad Pro 12.9": 2048x2732 pixels
iPad Pro 11": 1668x2388 pixels
```

### 🤖 Android Assets

#### Adaptive Icon Foreground
```
Size: 1024x1024 pixels
Format: PNG (32-bit with alpha)
Safe Area: 768x768 pixels (center)
Location: ./assets/images/adaptive-icon.png
Background: Transparent
```

#### Android Splash Screen
```
XXXHDPI (4.0x): 1080x1920 pixels
XXHDPI (3.0x): 810x1440 pixels
XHDPI (2.0x): 540x960 pixels
HDPI (1.5x): 405x720 pixels
MDPI (1.0x): 270x480 pixels
```

### 🌐 Web Assets

#### Favicon
```
Primary: 192x192 pixels
Secondary: 48x48 pixels
Format: PNG (32-bit with alpha)
Location: ./assets/images/favicon.png
```

#### Web App Manifest Icons
```
512x512 pixels (for PWA)
256x256 pixels
192x192 pixels
144x144 pixels
96x96 pixels
72x72 pixels
48x48 pixels
```

## 🎨 Design Specifications for Safqa Shipping

### Brand Colors
```css
Primary Background: #F5F5DC (Beige/Cream)
Logo Gold: #FFD700 (Gold)
Logo Text: #333333 (Dark Gray)
Accent: #FFA500 (Orange)
```

### Logo Specifications
```
Minimum Size: 120x40 pixels
Recommended Size: 300x100 pixels
Format: Vector (SVG source) → PNG export
Padding: 20px minimum around logo
Aspect Ratio: Maintain original proportions
```

### Typography
```
Primary Font: System default or custom brand font
Logo Text: Bold, high contrast
Minimum Size: 14px for readability
```

## 📐 Exact Pixel Requirements

### Main Splash Image (splash-icon.png)
```
Canvas Size: 1284x2778 pixels (iPhone 14 Pro Max)
Logo Position: Center (642x1389)
Logo Size: 200x67 pixels (approximate)
Background: Transparent PNG
Safe Area: 100px margin from edges
```

### App Icon Creation Guide
```
1. Create 1024x1024 canvas
2. Add 5% padding (51px) from edges
3. Center Safqa logo in 922x922 area
4. Use solid background (no transparency)
5. Ensure logo is readable at 29x29 pixels (smallest iOS size)
```

### Adaptive Icon Guide
```
1. Create 1024x1024 canvas
2. Safe area: 768x768 center (128px margin)
3. Logo should fit within safe area
4. Use transparent background
5. Test with different Android launcher shapes
```

## 🛠️ Asset Creation Workflow

### Step 1: Source Files
```
Create master logo file:
- Format: SVG or AI (vector)
- Size: Scalable
- Colors: Brand colors defined above
```

### Step 2: Export Settings
```
For PNG exports:
- Color Profile: sRGB
- Bit Depth: 24-bit (icons), 32-bit (splash with transparency)
- Compression: Optimized for file size
- Metadata: Remove EXIF data
```

### Step 3: Quality Check
```
- Test at smallest size (29x29 for iOS)
- Verify colors match brand guidelines
- Check transparency edges are clean
- Ensure file sizes are optimized
```

## 📁 File Structure
```
assets/
└── images/
    ├── icon.png              (1024x1024, iOS app icon)
    ├── adaptive-icon.png     (1024x1024, Android adaptive)
    ├── splash-icon.png       (1284x2778, main splash)
    ├── favicon.png           (192x192, web favicon)
    └── brand/                (optional brand assets folder)
        ├── logo-source.svg   (source vector file)
        ├── logo-gold.png     (gold version)
        └── logo-white.png    (white version for dark backgrounds)
```

## 🔧 Tools Recommended

### Design Tools
- **Adobe Illustrator**: Vector logo creation
- **Figma**: UI design and asset export
- **Sketch**: macOS design tool
- **Canva**: Quick logo modifications

### Optimization Tools
- **TinyPNG**: PNG compression
- **ImageOptim**: macOS image optimization
- **Squoosh**: Web-based image optimization

### Testing Tools
- **Expo Go**: Real device testing
- **iOS Simulator**: iOS testing
- **Android Emulator**: Android testing

## ✅ Quality Checklist

### Before Implementation
- [ ] All assets are correct dimensions
- [ ] File formats match requirements
- [ ] Colors match brand guidelines
- [ ] Logo is readable at smallest sizes
- [ ] File sizes are optimized
- [ ] No copyright issues with assets

### After Implementation
- [ ] Splash screen displays correctly
- [ ] App icon appears on home screen
- [ ] Web favicon shows in browser
- [ ] Android adaptive icon works with all launchers
- [ ] Assets look good on different screen densities
- [ ] Loading time is acceptable

## 🚨 Common Mistakes to Avoid

1. **Wrong Aspect Ratios**: Always maintain logo proportions
2. **Low Resolution**: Use high-DPI source images
3. **Incorrect Color Profiles**: Use sRGB for consistency
4. **Large File Sizes**: Optimize without quality loss
5. **Missing Safe Areas**: Keep important content within safe zones
6. **Transparency Issues**: Check alpha channels carefully

## 📞 Support Resources

- **Expo Splash Screen Docs**: https://docs.expo.dev/guides/splash-screens/
- **iOS Human Interface Guidelines**: Apple's design standards
- **Android Material Design**: Google's design principles
- **React Native Image Guide**: https://reactnative.dev/docs/images
