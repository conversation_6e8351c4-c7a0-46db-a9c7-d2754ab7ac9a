# Safqa Shipping - Splash Screen Setup Guide

## Overview
This guide provides complete instructions for setting up the splash screen for the Safqa Shipping React Native app using Expo's built-in splash screen functionality.

## Current Configuration
Your app already has `expo-splash-screen` installed and configured. We'll optimize it for the Safqa Shipping brand.

## Required Assets & Sizes

### 1. App Icons

#### iOS App Icon (icon.png)
- **Size**: 1024x1024 pixels
- **Format**: PNG (no transparency)
- **Location**: `./assets/images/icon.png`
- **Usage**: Main app icon for iOS App Store and device

#### Android Adaptive Icon
- **Foreground Image**: 1024x1024 pixels
- **Format**: PNG (with transparency)
- **Location**: `./assets/images/adaptive-icon.png`
- **Safe Area**: Keep important content within 768x768 center area
- **Usage**: Android adaptive icon foreground

### 2. Splash Screen Assets

#### Main Splash Image (splash-icon.png)
- **Size**: 1284x2778 pixels (iPhone 14 Pro Max resolution)
- **Format**: PNG (with transparency for logo)
- **Location**: `./assets/images/splash-icon.png`
- **Content**: Safqa Shipping logo centered
- **Background**: Transparent (background color set in config)

#### Alternative Sizes (Optional but Recommended)
- **Tablet**: 2048x2732 pixels
- **Android**: 1080x1920 pixels

### 3. Web Assets

#### Favicon
- **Size**: 48x48 pixels (minimum), 192x192 pixels (recommended)
- **Format**: PNG
- **Location**: `./assets/images/favicon.png`
- **Usage**: Browser tab icon

## Recommended App.json Configuration

```json
{
  "expo": {
    "name": "Safqa Shipping",
    "slug": "safqa-shipping",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/images/icon.png",
    "scheme": "safqashipping",
    "userInterfaceStyle": "automatic",
    "splash": {
      "image": "./assets/images/splash-icon.png",
      "resizeMode": "contain",
      "backgroundColor": "#F5F5DC"
    },
    "ios": {
      "supportsTablet": true,
      "icon": "./assets/images/icon.png"
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/images/adaptive-icon.png",
        "backgroundColor": "#F5F5DC"
      },
      "edgeToEdgeEnabled": true
    },
    "web": {
      "bundler": "metro",
      "output": "static",
      "favicon": "./assets/images/favicon.png"
    },
    "plugins": [
      "expo-router",
      [
        "expo-splash-screen",
        {
          "image": "./assets/images/splash-icon.png",
          "imageWidth": 200,
          "resizeMode": "contain",
          "backgroundColor": "#F5F5DC"
        }
      ]
    ]
  }
}
```

## Color Scheme
Based on your Safqa Shipping brand:
- **Primary Background**: `#F5F5DC` (Beige/Cream - matches your design)
- **Logo Colors**: Gold/Yellow (`#FFD700`) and Dark Gray (`#333333`)

## Asset Creation Guidelines

### 1. Logo Design
- Create a high-resolution version of your Safqa Shipping logo
- Ensure logo works on the beige background
- Keep logo crisp and readable at different sizes
- Use vector format (SVG) for source, export to PNG

### 2. Background Pattern
- For the subtle path pattern in your design, consider:
  - Adding it directly to the splash image
  - Or using a custom splash screen component (advanced)

### 3. File Naming Convention
```
assets/images/
├── icon.png              (1024x1024 - App icon)
├── adaptive-icon.png     (1024x1024 - Android adaptive)
├── splash-icon.png       (1284x2778 - Main splash)
├── favicon.png           (192x192 - Web favicon)
└── splash-background.png (Optional - Custom background)
```

## Implementation Steps

1. **Create/Replace Assets**: Add your Safqa Shipping branded assets to the specified locations
2. **Update app.json**: Apply the recommended configuration
3. **Test on Devices**: 
   - iOS: `expo start --ios`
   - Android: `expo start --android`
   - Web: `expo start --web`
4. **Build and Deploy**: Use `expo build` or EAS Build for production

## Advanced Customization (Optional)

For more complex splash screens with animations or custom layouts, you can:
1. Use `expo-splash-screen` API programmatically
2. Create a custom splash component
3. Implement animated transitions

## Testing Checklist

- [ ] App icon appears correctly on device home screen
- [ ] Splash screen displays with correct logo and background
- [ ] Splash screen works on both light and dark modes
- [ ] Web favicon shows in browser tab
- [ ] Android adaptive icon works with different launcher themes
- [ ] Splash screen timing feels natural (not too fast/slow)

## Troubleshooting

### Common Issues:
1. **Splash not updating**: Clear Expo cache with `expo start -c`
2. **Wrong aspect ratio**: Ensure splash image matches device ratios
3. **Blurry logo**: Use high-resolution source images
4. **Color mismatch**: Verify hex color codes in configuration

## Next Steps
After setting up the basic splash screen, consider:
- Adding loading animations
- Implementing progressive app loading
- Optimizing for different screen densities
- Adding brand-specific transitions
