# Safqa Shipping - Assets Guide

## Current Assets Status

### ✅ Required Files (Already Present)
- `icon.png` - Main app icon (needs to be replaced with Safqa logo)
- `adaptive-icon.png` - Android adaptive icon (needs to be replaced)
- `splash-icon.png` - Splash screen logo (needs to be replaced)
- `favicon.png` - Web favicon (needs to be replaced)

### 🎯 What You Need To Do

1. **Replace `icon.png`**
   - Size: 1024x1024 pixels
   - Your Safqa Shipping logo on solid background
   - No transparency
   - Format: PNG

2. **Replace `adaptive-icon.png`**
   - Size: 1024x1024 pixels
   - Your Safqa Shipping logo (foreground only)
   - Transparent background
   - Keep logo within 768x768 center area

3. **Replace `splash-icon.png`**
   - Size: 1284x2778 pixels (or at least 1080x1920)
   - Your Safqa Shipping logo centered
   - Transparent background (background color set in app.json)
   - Logo should be around 200px wide

4. **Replace `favicon.png`**
   - Size: 192x192 pixels
   - Your Safqa Shipping logo
   - Can have background or transparent

## Brand Colors Used in Configuration
- Background: `#F5F5DC` (Beige/Cream)
- This matches the background in your design mockup

## Quick Start
1. Create your assets using the sizes above
2. Replace the existing files in this folder
3. Run `expo start` to see your changes
4. Test on iOS: `expo start --ios`
5. Test on Android: `expo start --android`

## Need Help?
- Check `SPLASH_SCREEN_SETUP.md` for detailed instructions
- Check `ASSET_REQUIREMENTS.md` for exact specifications
- Use design tools like Figma, Canva, or Adobe Illustrator

## Testing Your Assets
After replacing the files:
```bash
# Clear cache and restart
expo start -c

# Test on different platforms
expo start --ios
expo start --android
expo start --web
```

Your splash screen will show:
1. Beige/cream background (#F5F5DC)
2. Your Safqa Shipping logo centered
3. Smooth transition to your app

## File Sizes (Keep Under These Limits)
- icon.png: < 1MB
- adaptive-icon.png: < 500KB
- splash-icon.png: < 2MB
- favicon.png: < 100KB
